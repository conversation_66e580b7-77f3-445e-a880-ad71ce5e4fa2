@echo off
echo =========================================
echo     Testing IMPROVED AgentRPC MCP Mode
echo       (With Crash Prevention)
echo =========================================
echo.

cd /d C:\Users\<USER>\Documents\agentrpc-toolkit

echo Setting environment variables...
set AGENTRPC_API_SECRET=sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799
set AGENTRPC_MODE=mcp

echo.
echo Starting IMPROVED server in MCP mode...
echo This version will NOT crash due to MongoDB or other connection issues!
echo.

node server.js

echo.
echo ===========================================
echo Server stopped. Press any key to exit.
pause
