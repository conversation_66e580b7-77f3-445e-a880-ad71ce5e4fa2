#!/usr/bin/env node

require('dotenv').config();
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { AgentRPC } = require('agentrpc');
const fs = require('fs');
const path = require('path');
const { z } = require('zod');
const { zodToJsonSchema } = require('zod-to-json-schema');

// Create MCP Server with proper capabilities
const server = new Server(
  {
    name: 'agentrpc-comprehensive',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
      resources: {},
      prompts: {}
    },
  }
);

console.error('🚀 Starting Comprehensive AgentRPC MCP Bridge...');
console.error('📋 Based on Master Analysis Report recommendations');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

// Tool collection for dual-protocol operation
const tools = [];
const toolHandlers = new Map();
let totalTools = 0;

// Create necessary directories
const dirs = ['temp', 'reports', 'backups'];
dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Enhanced logging
const log = {
  info: (...args) => console.error(`[INFO] ${new Date().toISOString()}:`, ...args),
  warn: (...args) => console.error(`[WARN] ${new Date().toISOString()}:`, ...args),
  error: (...args) => console.error(`[ERROR] ${new Date().toISOString()}:`, ...args),
  debug: (...args) => console.error(`[DEBUG] ${new Date().toISOString()}:`, ...args),
};

// Dual-protocol tool registration function
function registerTool(toolDef) {
  try {
    // Convert Zod schema to JSON Schema for MCP
    const inputSchema = zodToJsonSchema(toolDef.schema, {
      name: `${toolDef.name}Schema`,
      $refStrategy: 'none'
    });
    
    // Add to MCP tools list
    tools.push({
      name: toolDef.name,
      description: toolDef.description,
      inputSchema
    });
    
    // Store handler for MCP calls
    toolHandlers.set(toolDef.name, toolDef.handler);
    
    // Register with AgentRPC platform (async, non-blocking)
    rpc.register(toolDef).catch(error => {
      log.warn(`AgentRPC registration failed for ${toolDef.name}:`, error.message);
    });
    
    totalTools++;
    log.debug(`Registered dual-protocol tool: ${toolDef.name}`);
  } catch (error) {
    log.error(`Failed to register tool ${toolDef.name}:`, error.message);
  }
}

// Load comprehensive tool modules (Phase 2 implementation)
function loadToolModules() {
  const toolModules = [
    './tools/basic-tools.js',
    './tools/file-tools.js',
    './tools/development-tools.js',
    './tools/monitoring-tools.js',
    './tools/database-tools.js'
  ];

  // Mock RPC for tool collection
  const mockRpc = {
    register: registerTool
  };

  toolModules.forEach(modulePath => {
    try {
      if (fs.existsSync(modulePath)) {
        const toolModule = require(modulePath);
        if (typeof toolModule === 'function') {
          const beforeCount = totalTools;
          toolModule(mockRpc);
          const newTools = totalTools - beforeCount;
          log.info(`✅ Loaded ${newTools} tools from ${modulePath}`);
        }
      } else {
        log.debug(`Tool module not found: ${modulePath}`);
      }
    } catch (error) {
      log.warn(`Could not load ${modulePath}:`, error.message);
    }
  });
}

// Essential tools (Phase 1 - always available)
registerTool({
  name: 'testConnection',
  description: 'Test if AgentRPC MCP Bridge is working correctly',
  schema: z.object({
    message: z.string().optional().default('Hello AgentRPC!').describe('Test message')
  }),
  handler: async ({ message = 'Hello AgentRPC!' }) => {
    return {
      success: true,
      message,
      timestamp: new Date().toISOString(),
      server: 'AgentRPC Comprehensive MCP Bridge',
      testPassed: true,
      bridgeVersion: '1.0.0',
      totalToolsAvailable: totalTools
    };
  }
});

registerTool({
  name: 'getSystemInfo',
  description: 'Get comprehensive system information',
  schema: z.object({}),
  handler: async () => {
    const os = require('os');
    return {
      platform: os.platform(),
      architecture: os.arch(),
      nodeVersion: process.version,
      totalMemory: `${Math.round(os.totalmem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      freeMemory: `${Math.round(os.freemem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      cpuCount: os.cpus().length,
      uptime: `${Math.round(os.uptime() / 3600 * 100) / 100} hours`,
      hostname: os.hostname(),
      user: os.userInfo().username,
      workingDirectory: process.cwd(),
      bridgeInfo: {
        totalTools: totalTools,
        mcpProtocol: '2024-11-05',
        agentRpcConnected: 'checking...'
      }
    };
  }
});

registerTool({
  name: 'calculate',
  description: 'Perform mathematical calculations safely',
  schema: z.object({
    expression: z.string().describe('Mathematical expression to evaluate (e.g., "2 + 3 * 4", "sqrt(16)")')
  }),
  handler: async ({ expression }) => {
    try {
      // Enhanced sanitization for safety
      const sanitized = expression.replace(/[^0-9+\-*/.() ]/g, '');
      if (sanitized !== expression) {
        log.warn(`Expression sanitized: "${expression}" -> "${sanitized}"`);
      }
      const result = eval(sanitized);
      
      return {
        expression: expression,
        sanitized: sanitized,
        result: result,
        formatted: `${expression} = ${result}`,
        calculatedAt: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Invalid mathematical expression: ${error.message}`);
    }
  }
});

registerTool({
  name: 'listFiles',
  description: 'List files and directories with detailed information',
  schema: z.object({
    directory: z.string().describe('Directory path to list'),
    includeHidden: z.boolean().optional().default(false).describe('Include hidden files')
  }),
  handler: async ({ directory, includeHidden = false }) => {
    try {
      const items = fs.readdirSync(directory, { withFileTypes: true });
      const filteredItems = includeHidden ? items : items.filter(item => !item.name.startsWith('.'));
      
      return {
        directory,
        items: filteredItems.map(item => {
          const fullPath = path.join(directory, item.name);
          const stats = fs.statSync(fullPath);
          return {
            name: item.name,
            type: item.isDirectory() ? 'directory' : 'file',
            path: fullPath,
            size: item.isFile() ? stats.size : null,
            lastModified: stats.mtime.toISOString(),
            permissions: stats.mode
          };
        }),
        count: filteredItems.length,
        scannedAt: new Date().toISOString(),
        includeHidden
      };
    } catch (error) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  }
});

registerTool({
  name: 'readFile',
  description: 'Read file contents with metadata',
  schema: z.object({
    filePath: z.string().describe('Path to the file to read'),
    encoding: z.string().default('utf8').describe('File encoding'),
    maxSize: z.number().optional().default(1048576).describe('Maximum file size to read (bytes)')
  }),
  handler: async ({ filePath, encoding = 'utf8', maxSize = 1048576 }) => {
    try {
      const stats = fs.statSync(filePath);
      
      if (stats.size > maxSize) {
        throw new Error(`File too large: ${stats.size} bytes (max: ${maxSize})`);
      }
      
      const content = fs.readFileSync(filePath, encoding);
      
      return {
        content,
        filePath,
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        encoding,
        readAt: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }
});

registerTool({
  name: 'checkWebsite',
  description: 'Check website availability and response time',
  schema: z.object({
    url: z.string().describe('Website URL to check'),
    timeout: z.number().optional().default(10000).describe('Timeout in milliseconds')
  }),
  handler: async ({ url, timeout = 10000 }) => {
    try {
      const axios = require('axios');
      const startTime = Date.now();
      const response = await axios.get(url, { 
        timeout,
        validateStatus: () => true // Accept any status code
      });
      const responseTime = Date.now() - startTime;
      
      return {
        url,
        status: response.status < 400 ? 'UP' : 'DOWN',
        statusCode: response.status,
        statusText: response.statusText,
        responseTime: `${responseTime}ms`,
        headers: response.headers,
        checkedAt: new Date().toISOString()
      };
    } catch (error) {
      return {
        url,
        status: 'DOWN',
        error: error.message,
        errorCode: error.code,
        checkedAt: new Date().toISOString()
      };
    }
  }
});

// Load additional tool modules
loadToolModules();

log.info(`🎉 Successfully registered ${totalTools} tools for dual-protocol operation`);

// MCP Protocol Handlers (Phase 1 requirement)
server.setRequestHandler('tools/list', async () => {
  log.debug('MCP tools/list called');
  return { tools };
});

server.setRequestHandler('resources/list', async () => {
  log.debug('MCP resources/list called');
  return { resources: [] }; // Required by MCP spec
});

server.setRequestHandler('prompts/list', async () => {
  log.debug('MCP prompts/list called');
  return { prompts: [] }; // Required by MCP spec
});

server.setRequestHandler('tools/call', async (request) => {
  const { name, arguments: args = {} } = request.params;
  log.debug(`MCP tools/call for: ${name}`);
  
  try {
    if (toolHandlers.has(name)) {
      const handler = toolHandlers.get(name);
      const result = await handler(args);
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } else {
      throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    log.error(`Tool execution failed for ${name}:`, error.message);
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: false,
          error: error.message,
          tool: name,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
});

log.info('✅ All required MCP handlers registered');

// Start AgentRPC connection (Phase 1 requirement)
rpc.listen().then(() => {
  log.info('✅ Connected to AgentRPC platform for coordination');
}).catch(error => {
  log.warn(`AgentRPC platform connection failed: ${error.message}`);
  log.info('💡 Bridge will continue with MCP-only operation');
});

// Start MCP server
const transport = new StdioServerTransport();
server.connect(transport).then(() => {
  log.info('🔗 Comprehensive MCP Bridge connected and ready!');
  log.info(`🛠️  ${totalTools} tools available through Claude Desktop`);
  log.info('✨ Dual-protocol operation: AgentRPC + MCP');
}).catch((error) => {
  log.error(`Failed to start MCP Bridge: ${error.message}`);
  process.exit(1);
});

// Enhanced error handling
process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled Promise Rejection:', reason);
});

process.on('uncaughtException', (error) => {
  log.error('Uncaught Exception:', error.message);
  log.error('Stack:', error.stack);
});

process.on('SIGINT', () => {
  log.info('Shutting down AgentRPC MCP Bridge...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log.info('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
