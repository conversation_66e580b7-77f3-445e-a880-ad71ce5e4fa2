@echo off
echo 🚀 Starting Both AgentRPC and MCP Bridge Servers
echo 📋 This will connect to both AgentRPC Dashboard AND Claude Desktop
echo.

REM Set environment variables
set AGENTRPC_API_SECRET=sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799

echo 📦 Environment:
echo   AGENTRPC_API_SECRET: %AGENTRPC_API_SECRET%
echo   Working Directory: %CD%
echo.

echo 🔍 Pre-flight checks:
if exist "agentrpc-server-simple.js" (
    echo   ✅ AgentRPC server file exists
) else (
    echo   ❌ AgentRPC server file missing
    goto :error
)

if exist "mcp-bridge-simple.js" (
    echo   ✅ MCP bridge file exists
) else (
    echo   ❌ MCP bridge file missing
    goto :error
)

echo.
echo 🌐 This will start TWO servers:
echo   1. AgentRPC Server - for dashboard tools
echo   2. MCP Bridge - for Claude Desktop integration
echo.

echo 🚀 Starting AgentRPC server in background...
start "AgentRPC Server" cmd /k "echo AgentRPC Server Running && node agentrpc-server-simple.js"

echo ⏳ Waiting 3 seconds for AgentRPC server to start...
timeout /t 3 /nobreak >nul

echo 🔗 Starting MCP Bridge for <PERSON>...
echo 💡 Keep this window open for Claude Desktop connection
echo 📊 Check dashboard: https://app.agentrpc.com/clusters/01JWJFP133VC25GQNQSV91ZX6E
echo.

node mcp-bridge-simple.js

goto :end

:error
echo.
echo ❌ Startup failed - missing files
echo 💡 Ensure both server files exist in current directory
echo.

:end
pause
