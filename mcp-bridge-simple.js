#!/usr/bin/env node

require('dotenv').config();
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { AgentRPC } = require('agentrpc');
const fs = require('fs');
const path = require('path');
const { z } = require('zod');
const { zodToJsonSchema } = require('zod-to-json-schema');

// Create MCP Server with proper capabilities
const server = new Server(
  {
    name: 'agentrpc-simple',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
      resources: {},
      prompts: {}
    },
  }
);

console.error('🚀 Starting Simple AgentRPC MCP Bridge...');
console.error('📋 8 Essential Tools Only');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

// Tool collection for dual-protocol operation
const tools = [];
const toolHandlers = new Map();

// Enhanced logging
const log = {
  info: (...args) => console.error(`[INFO] ${new Date().toISOString()}:`, ...args),
  warn: (...args) => console.error(`[WARN] ${new Date().toISOString()}:`, ...args),
  error: (...args) => console.error(`[ERROR] ${new Date().toISOString()}:`, ...args),
  debug: (...args) => console.error(`[DEBUG] ${new Date().toISOString()}:`, ...args),
};

// Simple tool registration function
function registerTool(toolDef) {
  try {
    // Convert Zod schema to JSON Schema for MCP
    const inputSchema = zodToJsonSchema(toolDef.schema, {
      name: `${toolDef.name}Schema`,
      $refStrategy: 'none'
    });
    
    // Add to MCP tools list
    tools.push({
      name: toolDef.name,
      description: toolDef.description,
      inputSchema
    });
    
    // Store handler for MCP calls
    toolHandlers.set(toolDef.name, toolDef.handler);
    
    // Register with AgentRPC platform (async, non-blocking)
    rpc.register(toolDef).catch(error => {
      log.warn(`AgentRPC registration failed for ${toolDef.name}:`, error.message);
    });
    
    log.debug(`Registered tool: ${toolDef.name}`);
  } catch (error) {
    log.error(`Failed to register tool ${toolDef.name}:`, error.message);
  }
}

// 8 Essential Tools Only
log.info('📦 Loading 8 essential tools...');

// 1. Test Connection
registerTool({
  name: 'testConnection',
  description: 'Test if AgentRPC MCP Bridge is working correctly',
  schema: z.object({
    message: z.string().optional().default('Hello AgentRPC!').describe('Test message')
  }),
  handler: async ({ message = 'Hello AgentRPC!' }) => {
    return {
      success: true,
      message,
      timestamp: new Date().toISOString(),
      server: 'AgentRPC Simple MCP Bridge',
      testPassed: true,
      toolCount: tools.length
    };
  }
});

// 2. System Info
registerTool({
  name: 'getSystemInfo',
  description: 'Get basic system information',
  schema: z.object({}),
  handler: async () => {
    const os = require('os');
    return {
      platform: os.platform(),
      architecture: os.arch(),
      nodeVersion: process.version,
      totalMemory: `${Math.round(os.totalmem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      freeMemory: `${Math.round(os.freemem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      cpuCount: os.cpus().length,
      hostname: os.hostname(),
      user: os.userInfo().username
    };
  }
});

// 3. Calculator
registerTool({
  name: 'calculate',
  description: 'Perform basic mathematical calculations',
  schema: z.object({
    expression: z.string().describe('Mathematical expression (e.g., "2 + 3 * 4")')
  }),
  handler: async ({ expression }) => {
    try {
      // Basic sanitization
      const sanitized = expression.replace(/[^0-9+\-*/.() ]/g, '');
      const result = eval(sanitized);
      
      return {
        expression,
        result,
        formatted: `${expression} = ${result}`
      };
    } catch (error) {
      throw new Error(`Invalid expression: ${error.message}`);
    }
  }
});

// 4. List Files
registerTool({
  name: 'listFiles',
  description: 'List files in a directory',
  schema: z.object({
    directory: z.string().describe('Directory path to list')
  }),
  handler: async ({ directory }) => {
    try {
      const items = fs.readdirSync(directory, { withFileTypes: true });
      return {
        directory,
        items: items.map(item => ({
          name: item.name,
          type: item.isDirectory() ? 'directory' : 'file'
        })),
        count: items.length
      };
    } catch (error) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  }
});

// 5. Read File
registerTool({
  name: 'readFile',
  description: 'Read contents of a text file',
  schema: z.object({
    filePath: z.string().describe('Path to the file to read')
  }),
  handler: async ({ filePath }) => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const stats = fs.statSync(filePath);
      
      return {
        content,
        filePath,
        size: stats.size,
        lastModified: stats.mtime.toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }
});

// 6. Check Website
registerTool({
  name: 'checkWebsite',
  description: 'Check if a website is responding',
  schema: z.object({
    url: z.string().describe('Website URL to check')
  }),
  handler: async ({ url }) => {
    try {
      const axios = require('axios');
      const startTime = Date.now();
      const response = await axios.get(url, { timeout: 10000 });
      const responseTime = Date.now() - startTime;
      
      return {
        url,
        status: 'UP',
        statusCode: response.status,
        responseTime: `${responseTime}ms`
      };
    } catch (error) {
      return {
        url,
        status: 'DOWN',
        error: error.message
      };
    }
  }
});

// 7. Get Weather (mock)
registerTool({
  name: 'getWeather',
  description: 'Get weather information for a location',
  schema: z.object({
    location: z.string().describe('Location to get weather for')
  }),
  handler: async ({ location }) => {
    // Mock weather data for testing
    return {
      location,
      temperature: '75°F',
      condition: 'sunny',
      humidity: '45%',
      timestamp: new Date().toISOString(),
      note: 'Mock weather data for testing'
    };
  }
});

// 8. Current Time
registerTool({
  name: 'getCurrentTime',
  description: 'Get current date and time',
  schema: z.object({
    timezone: z.string().optional().describe('Timezone (optional)')
  }),
  handler: async ({ timezone }) => {
    const now = new Date();
    return {
      timestamp: now.toISOString(),
      localTime: now.toLocaleString(),
      timezone: timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      unixTimestamp: Math.floor(now.getTime() / 1000)
    };
  }
});

log.info(`✅ Successfully registered ${tools.length} essential tools`);

// MCP Protocol Handlers (ALL REQUIRED METHODS)
server.setRequestHandler('tools/list', async () => {
  log.debug('MCP tools/list called');
  return { tools };
});

server.setRequestHandler('resources/list', async () => {
  log.debug('MCP resources/list called');
  return { resources: [] }; // Required by MCP spec
});

server.setRequestHandler('prompts/list', async () => {
  log.debug('MCP prompts/list called');
  return { prompts: [] }; // Required by MCP spec
});

server.setRequestHandler('tools/call', async (request) => {
  const { name, arguments: args = {} } = request.params;
  log.debug(`MCP tools/call for: ${name}`);
  
  try {
    if (toolHandlers.has(name)) {
      const handler = toolHandlers.get(name);
      const result = await handler(args);
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } else {
      throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    log.error(`Tool execution failed for ${name}:`, error.message);
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: false,
          error: error.message,
          tool: name,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
});

log.info('✅ All required MCP handlers registered');

// Start AgentRPC connection (optional)
rpc.listen().then(() => {
  log.info('✅ Connected to AgentRPC platform');
}).catch(error => {
  log.warn(`AgentRPC platform connection failed: ${error.message}`);
  log.info('💡 Bridge will continue with MCP-only operation');
});

// Start MCP server
const transport = new StdioServerTransport();
server.connect(transport).then(() => {
  log.info('🔗 Simple MCP Bridge connected and ready!');
  log.info(`🛠️  ${tools.length} tools available through Claude Desktop`);
}).catch((error) => {
  log.error(`Failed to start MCP Bridge: ${error.message}`);
  process.exit(1);
});

// Error handling
process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled Promise Rejection:', reason);
});

process.on('uncaughtException', (error) => {
  log.error('Uncaught Exception:', error.message);
});

process.on('SIGINT', () => {
  log.info('Shutting down Simple AgentRPC MCP Bridge...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log.info('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
