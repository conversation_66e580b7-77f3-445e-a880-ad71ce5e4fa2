#!/usr/bin/env node

require('dotenv').config();

console.log('🧪 Testing AgentRPC Server Connection...');
console.log('');

// Check environment
console.log('📦 Environment Check:');
console.log(`   AGENTRPC_API_SECRET: ${process.env.AGENTRPC_API_SECRET ? 'SET' : 'NOT SET'}`);
console.log(`   Working Directory: ${process.cwd()}`);
console.log('');

// Check if AgentRPC package is available
try {
  const { AgentRPC } = require('agentrpc');
  console.log('✅ AgentRPC package is available');
  
  // Test connection
  console.log('🔗 Testing connection to AgentRPC platform...');
  
  const rpc = new AgentRPC({
    apiSecret: process.env.AGENTRPC_API_SECRET,
  });
  
  // Register a simple test tool
  rpc.register({
    name: 'connectionTest',
    description: 'Simple connection test',
    schema: require('zod').object({}),
    handler: async () => {
      return { success: true, timestamp: new Date().toISOString() };
    }
  });
  
  rpc.listen()
    .then(() => {
      console.log('✅ Successfully connected to AgentRPC platform!');
      console.log('🌐 Check your dashboard: https://app.agentrpc.com/clusters/01JWJFP133VC25GQNQSV91ZX6E');
      console.log('🔄 Refresh the page to see the connectionTest tool');
      console.log('');
      console.log('💡 If you see this message, the server is working correctly!');
      
      // Keep running for 30 seconds then exit
      setTimeout(() => {
        console.log('');
        console.log('✨ Test completed successfully!');
        console.log('🚀 You can now run the full server: node agentrpc-server-simple.js');
        process.exit(0);
      }, 30000);
    })
    .catch(error => {
      console.error('❌ Connection failed:', error.message);
      console.error('');
      console.error('🔍 Troubleshooting:');
      console.error('   1. Check your API secret');
      console.error('   2. Verify internet connection');
      console.error('   3. Check firewall settings');
      process.exit(1);
    });
    
} catch (error) {
  console.error('❌ AgentRPC package not found:', error.message);
  console.error('💡 Run: npm install agentrpc');
  process.exit(1);
}
