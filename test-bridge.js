// Quick test to verify the fixed bridge works
const { spawn } = require('child_process');

console.log('Testing fixed-mcp-bridge.js...');

const bridge = spawn('node', ['fixed-mcp-bridge.js'], {
  cwd: 'C:\\Users\\<USER>\\Documents\\agentrpc-toolkit',
  stdio: ['pipe', 'pipe', 'pipe']
});

// Listen for startup messages
bridge.stderr.on('data', (data) => {
  console.log('Bridge output:', data.toString());
});

bridge.stdout.on('data', (data) => {
  console.log('Bridge stdout:', data.toString());
});

bridge.on('error', (error) => {
  console.error('Bridge failed to start:', error.message);
});

// Test basic initialization
setTimeout(() => {
  console.log('Sending initialize request...');
  
  const initRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' }
    }
  };
  
  bridge.stdin.write(JSON.stringify(initRequest) + '\n');
  
  // Clean shutdown after test
  setTimeout(() => {
    console.log('Test complete, shutting down...');
    bridge.kill();
  }, 2000);
  
}, 1000);
