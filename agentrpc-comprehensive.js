require('dotenv').config();
const { AgentRPC } = require('agentrpc');

console.error('🚀 Starting AgentRPC Comprehensive Toolkit...');
console.error('📦 Loading tool modules...');

// Initialize AgentRPC with your API secret
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET || 'sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799',
});

// Import all tool modules and register them with AgentRPC platform
const fs = require('fs');
const path = require('path');

const toolModules = [
  './tools/basic-tools',
  './tools/email-tools',
  './tools/calendar-tools',
  './tools/file-tools',
  './tools/document-tools',
  './tools/development-tools',
  './tools/database-tools',
  './tools/finance-tools',
  './tools/analytics-tools',
  './tools/monitoring-tools',
  './tools/media-tools',
  './tools/location-tools',
  './tools/rest-api-tools'
];

let totalTools = 0;

// Validate API Secret before proceeding
if (!process.env.AGENTRPC_API_SECRET) {
  console.error('[CRITICAL ERROR] 🚨 No AgentRPC API Secret found in .env file!');
  console.error('Please set AGENTRPC_API_SECRET in your .env file.');
  process.exit(1);
}

// Check if tools directory exists
const toolsDir = path.join(__dirname, 'tools');
if (!fs.existsSync(toolsDir)) {
  console.error(`[CRITICAL ERROR] 🚨 Tools directory not found: ${toolsDir}`);
  console.error('Please create the "tools" directory and add the required tool modules.');
  process.exit(1);
}

// Load each tool module
toolModules.forEach(modulePath => {
  try {
    const fullModulePath = path.join(__dirname, modulePath);
    
    // Check if module file exists before requiring
    if (!fs.existsSync(fullModulePath + '.js')) {
      console.error(`[WARNING] ⚠️ Tool module not found: ${fullModulePath}.js`);
      return;
    }

    const toolModule = require(fullModulePath);
    if (typeof toolModule === 'function') {
      const toolsCount = totalTools;
      const tools = toolModule(rpc); // Register tools with AgentRPC platform
      const newToolsCount = tools ? tools.length : (totalTools - toolsCount);
      console.error(`[INFO] ✅ Loaded ${newToolsCount} tools from ${modulePath}`);
      totalTools += newToolsCount;
    }
  } catch (error) {
    console.error(`[ERROR] ⚠️ Could not load ${modulePath}:`, error.message);
    console.error('Detailed Error:', error);
  }
});

console.error(`[INFO] 🎉 Successfully loaded ${totalTools} tools total`);

// Start listening for function calls from AgentRPC platform
console.error('[INFO] 🔗 Starting AgentRPC platform connection...');
try {
  rpc.listen();
  console.error('[INFO] ✨ AgentRPC Comprehensive Toolkit is ready!');
  console.error('[INFO] 🔧 All tools are now available through AgentRPC platform!');
} catch (connectionError) {
  console.error('[ERROR] 🚨 Failed to start AgentRPC connection:', connectionError);
  console.error('[ERROR] Possible reasons:');
  console.error('1. Invalid API Secret');
  console.error('2. Network connectivity issues');
  console.error('3. AgentRPC service is down');
  console.error('4. Firewall or proxy blocking connection');
  process.exit(1);
}

// Add more detailed error handling for RPC events
rpc.on('error', (error) => {
  console.error('[RPC ERROR] 🔥 An RPC error occurred:', error);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.error('\n👋 Shutting down AgentRPC Comprehensive Toolkit...');
  process.exit(0);
});
