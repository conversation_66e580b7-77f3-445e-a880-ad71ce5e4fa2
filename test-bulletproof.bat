@echo off
echo =========================================
echo     AgentRPC BULLETPROOF Server
echo       (Maximum Crash Resistance)
echo =========================================
echo.

cd /d C:\Users\<USER>\Documents\agentrpc-toolkit

echo Setting environment variables...
set AGENTRPC_API_SECRET=sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799
set AGENTRPC_MODE=mcp

echo.
echo Starting BULLETPROOF server...
echo This version addresses ALL identified crash causes:
echo   ✅ Individual tool error handling
echo   ✅ No external dependencies (axios, eval, etc.)
echo   ✅ Proper async/await patterns
echo   ✅ Defensive programming throughout
echo   ✅ No filesystem operations that could fail
echo.
echo Available bulletproof tools:
echo   🧪 testConnection - Always works
echo   🔧 getSystemInfo - Basic system info
echo   📢 echo - Echo back messages
echo   ⏰ getCurrentTime - Current date/time
echo.

node server-bulletproof.js

echo.
echo ===========================================
echo Server stopped. Press any key to exit.
pause
