#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');

const server = new Server(
  {
    name: 'agentrpc-bridge',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

console.error('🚀 Starting AgentRPC MCP Bridge...');

// Define tools
const tools = [
  {
    name: 'getWeather',
    description: 'Get weather information for any location',
    inputSchema: {
      type: 'object',
      properties: {
        location: {
          type: 'string',
          description: 'Location to get weather for'
        }
      },
      required: ['location']
    }
  },
  {
    name: 'calculate',
    description: 'Perform mathematical calculations',
    inputSchema: {
      type: 'object',
      properties: {
        a: { type: 'number', description: 'First number' },
        b: { type: 'number', description: 'Second number' },
        operation: { 
          type: 'string', 
          enum: ['add', 'subtract', 'multiply', 'divide'],
          description: 'Operation to perform' 
        }
      },
      required: ['a', 'b', 'operation']
    }
  }
];

// Handle tools/list
server.setRequestHandler('tools/list', async () => {
  console.error('[DEBUG] tools/list called');
  return { tools };
});

// Handle resources/list (REQUIRED for MCP handshake)
server.setRequestHandler('resources/list', async () => {
  console.error('[DEBUG] resources/list called');
  return { resources: [] }; // Empty array is fine
});

// Handle prompts/list (REQUIRED for MCP handshake)
server.setRequestHandler('prompts/list', async () => {
  console.error('[DEBUG] prompts/list called');
  return { prompts: [] }; // Empty array is fine
});

// Handle tools/call
server.setRequestHandler('tools/call', async (request) => {
  console.error(`[DEBUG] tools/call called with: ${JSON.stringify(request.params)}`);
  
  const { name, arguments: args = {} } = request.params;
  
  try {
    if (name === 'getWeather') {
      const result = {
        location: args.location || 'Unknown',
        temperature: '75°F',
        condition: 'sunny',
        humidity: '45%',
        timestamp: new Date().toISOString()
      };
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    }
    
    if (name === 'calculate') {
      let result;
      const { a = 0, b = 0, operation = 'add' } = args;
      
      switch (operation) {
        case 'add': result = a + b; break;
        case 'subtract': result = a - b; break;
        case 'multiply': result = a * b; break;
        case 'divide': result = b !== 0 ? a / b : 'Error: Division by zero'; break;
        default: result = 'Unknown operation';
      }
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify({ a, b, operation, result }, null, 2)
        }]
      };
    }
    
    throw new Error(`Unknown tool: ${name}`);
    
  } catch (error) {
    console.error(`[ERROR] Tool execution failed: ${error.message}`);
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({ error: error.message }, null, 2)
      }]
    };
  }
});

console.error('[INFO] ✅ MCP Bridge handlers registered');

// Start server
const transport = new StdioServerTransport();
server.connect(transport).then(() => {
  console.error('[INFO] 🔗 MCP Bridge connected and ready!');
}).catch((error) => {
  console.error(`[ERROR] Failed to start MCP Bridge: ${error.message}`);
  process.exit(1);
});
