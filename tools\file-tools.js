const { z } = require('zod');
const fs = require('fs-extra');
const path = require('path');

module.exports = function(rpc) {
  const tools = [];

  // Read local file
  rpc.register({
    name: 'readLocalFile',
    description: 'Read contents of a local file',
    schema: z.object({
      filePath: z.string().describe('Path to the file to read'),
      encoding: z.string().default('utf8').describe('File encoding (utf8, binary, etc.)')
    }),
    handler: async ({ filePath, encoding = 'utf8' }) => {
      try {
        const content = await fs.readFile(filePath, encoding);
        const stats = await fs.stat(filePath);
        
        return {
          content,
          filePath,
          size: stats.size,
          lastModified: stats.mtime.toISOString(),
          encoding
        };
      } catch (error) {
        throw new Error(`Failed to read file: ${error.message}`);
      }
    }
  });
  tools.push('readLocalFile');

  // Write local file
  rpc.register({
    name: 'writeLocalFile',
    description: 'Write content to a local file (creates directories if needed)',
    schema: z.object({
      filePath: z.string().describe('Path where to save the file'),
      content: z.string().describe('Content to write to the file'),
      encoding: z.string().default('utf8').describe('File encoding (utf8, binary, etc.)'),
      createDirectories: z.boolean().default(true).describe('Create parent directories if they don\'t exist')
    }),
    handler: async ({ filePath, content, encoding = 'utf8', createDirectories = true }) => {
      try {
        // Validate file path
        if (!filePath || typeof filePath !== 'string') {
          throw new Error('Valid file path is required');
        }
        
        if (typeof content !== 'string') {
          throw new Error('Content must be a string');
        }
        
        // Create parent directories if needed
        if (createDirectories) {
          const dirPath = path.dirname(filePath);
          await fs.ensureDir(dirPath);
        }
        
        // Write the file
        await fs.writeFile(filePath, content, encoding);
        
        // Get file stats for confirmation
        const stats = await fs.stat(filePath);
        
        return {
          success: true,
          filePath: path.resolve(filePath),
          size: stats.size,
          created: stats.birthtime.toISOString(),
          lastModified: stats.mtime.toISOString(),
          encoding,
          message: `Successfully wrote ${stats.size} bytes to ${filePath}`
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          filePath,
          timestamp: new Date().toISOString()
        };
      }
    }
  });
  tools.push('writeLocalFile');

  // List directory contents
  rpc.register({
    name: 'listDirectory',
    description: 'List contents of a directory',
    schema: z.object({
      directoryPath: z.string().describe('Path to the directory'),
      recursive: z.boolean().default(false).describe('Include subdirectories recursively')
    }),
    handler: async ({ directoryPath, recursive = false }) => {
      try {
        if (!recursive) {
          const items = await fs.readdir(directoryPath, { withFileTypes: true });
          return {
            path: directoryPath,
            items: items.map(item => ({
              name: item.name,
              type: item.isDirectory() ? 'directory' : 'file',
              path: path.join(directoryPath, item.name)
            }))
          };
        } else {
          // Recursive listing
          const getAllFiles = async (dirPath, arrayOfFiles = []) => {
            const files = await fs.readdir(dirPath, { withFileTypes: true });
            
            for (const file of files) {
              const fullPath = path.join(dirPath, file.name);
              if (file.isDirectory()) {
                arrayOfFiles.push({ name: file.name, type: 'directory', path: fullPath });
                await getAllFiles(fullPath, arrayOfFiles);
              } else {
                arrayOfFiles.push({ name: file.name, type: 'file', path: fullPath });
              }
            }
            
            return arrayOfFiles;
          };
          
          const items = await getAllFiles(directoryPath);
          return {
            path: directoryPath,
            items,
            recursive: true
          };
        }
      } catch (error) {
        throw new Error(`Failed to list directory: ${error.message}`);
      }
    }
  });
  tools.push('listDirectory');

  // Copy file
  rpc.register({
    name: 'copyFile',
    description: 'Copy a file from source to destination',
    schema: z.object({
      sourcePath: z.string().describe('Source file path'),
      destinationPath: z.string().describe('Destination file path')
    }),
    handler: async ({ sourcePath, destinationPath }) => {
      try {
        await fs.ensureDir(path.dirname(destinationPath));
        await fs.copy(sourcePath, destinationPath);
        
        return {
          success: true,
          sourcePath,
          destinationPath,
          copiedAt: new Date().toISOString()
        };
      } catch (error) {
        throw new Error(`Failed to copy file: ${error.message}`);
      }
    }
  });
  tools.push('copyFile');

  // Delete file or directory
  rpc.register({
    name: 'deleteFileOrDirectory',
    description: 'Delete a file or directory',
    schema: z.object({
      targetPath: z.string().describe('Path to file or directory to delete'),
      recursive: z.boolean().default(false).describe('Delete directory recursively')
    }),
    handler: async ({ targetPath, recursive = false }) => {
      try {
        const stats = await fs.stat(targetPath);
        
        if (stats.isDirectory() && recursive) {
          await fs.remove(targetPath);
        } else if (stats.isFile()) {
          await fs.unlink(targetPath);
        } else {
          throw new Error('Cannot delete directory without recursive option');
        }
        
        return {
          success: true,
          deletedPath: targetPath,
          type: stats.isDirectory() ? 'directory' : 'file',
          deletedAt: new Date().toISOString()
        };
      } catch (error) {
        throw new Error(`Failed to delete: ${error.message}`);
      }
    }
  });
  tools.push('deleteFileOrDirectory');

  // Search files
  rpc.register({
    name: 'searchFiles',
    description: 'Search for files by name pattern in a directory',
    schema: z.object({
      directory: z.string().describe('Directory to search in'),
      pattern: z.string().describe('File name pattern (supports wildcards)'),
      recursive: z.boolean().default(true).describe('Search recursively in subdirectories')
    }),
    handler: async ({ directory, pattern, recursive = true }) => {
      try {
        const glob = require('glob');
        const searchPattern = recursive ? 
          path.join(directory, '**', pattern) : 
          path.join(directory, pattern);
        
        const files = await new Promise((resolve, reject) => {
          glob(searchPattern, (err, matches) => {
            if (err) reject(err);
            else resolve(matches);
          });
        });
        
        const results = await Promise.all(files.map(async (file) => {
          const stats = await fs.stat(file);
          return {
            path: file,
            name: path.basename(file),
            size: stats.size,
            lastModified: stats.mtime.toISOString()
          };
        }));
        
        return {
          searchPattern: pattern,
          directory,
          recursive,
          found: results.length,
          files: results
        };
      } catch (error) {
        throw new Error(`Search failed: ${error.message}`);
      }
    }
  });
  tools.push('searchFiles');

  return tools;
};
