{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-filesystem\",\n        \"C:\\\\Users\\\\<USER>\\\\Desktop\",\n        \"C:\\\\Users\\\\<USER>\\\\Downloads\",\n        \"C:\\\\Users\\\\<USER>\\\\Documents\",\n        \"C:\\\\Users\\\\<USER>\\\\AppData\"\n      ]\n    },\n    \"github\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-github\"\n      ],\n      \"env\": {\n        \"GITHUB_PERSONAL_ACCESS_TOKEN\": \"****************************************\"\n      }\n    },\n    \"agentql\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"agentql-mcp\"\n      ],\n      \"env\": {\n        \"AGENTQL_API_KEY\": \"8ZNb_0CUbwD7lhfaugDF4TVFyfbjyC9C9e4G5r7GtOi1vk3gm6g\"\n      }\n    },\n    \"puppeteer\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-puppeteer\"\n      ]\n    },\n    \"sequential-thinking\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-sequential-thinking\"\n      ]\n    },\n    \"google-maps\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"@modelcontextprotocol/server-google-maps\"],\n      \"env\": {\n        \"GOOGLE_MAPS_API_KEY\": \"AIzaSyASNmi3BzMG5eEBEMy1M2mqWlIM-F3s15o\"\n      }\n    },\n    \"git\": {\n      \"command\": \"uvx\",\n      \"args\": [\"mcp-server-git\", \"--repository\", \"C:/Users/<USER>/Desktop\"]\n    },\n    \"memory\": {\n      \"command\": \"npx\",\n      \"args\": [\n        \"-y\",\n        \"@modelcontextprotocol/server-memory\"\n      ]\n    },\n    \"agentrpc-essential\": {\n      \"command\": \"node\",\n      \"args\": [\"server-essential.js\"],\n      \"cwd\": \"C:\\\\Users\\\\<USER>\\\\Documents\\\\agentrpc-toolkit\",\n      \"env\": {\n        \"AGENTRPC_API_SECRET\": \"sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799\",\n        \"AGENTRPC_MODE\": \"mcp\"\n      }\n    }\n  }\n}\n