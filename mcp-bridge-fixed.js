#!/usr/bin/env node

require('dotenv').config();
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { AgentRPC } = require('agentrpc');
const fs = require('fs');
const path = require('path');
const { z } = require('zod');
const { zodToJsonSchema } = require('zod-to-json-schema');

// Create MCP Server
const server = new Server(
  {
    name: 'agentrpc-bridge',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

console.error('🚀 Starting AgentRPC MCP Bridge...');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

// Tool collection
const tools = [];
const toolHandlers = new Map();

// Helper function to register tools for both AgentRPC and MCP
function registerTool(toolDef) {
  try {
    // Convert Zod schema to JSON Schema for MCP
    const inputSchema = zodToJsonSchema(toolDef.schema, {
      name: `${toolDef.name}Schema`,
      $refStrategy: 'none'
    });
    
    // Add to MCP tools list
    tools.push({
      name: toolDef.name,
      description: toolDef.description,
      inputSchema
    });
    
    // Store handler
    toolHandlers.set(toolDef.name, toolDef.handler);
    
    // Register with AgentRPC platform
    rpc.register(toolDef).catch(error => {
      console.error(`[WARN] AgentRPC registration failed for ${toolDef.name}:`, error.message);
    });
    
    console.error(`[INFO] ✅ Registered tool: ${toolDef.name}`);
  } catch (error) {
    console.error(`[ERROR] Failed to register tool ${toolDef.name}:`, error.message);
  }
}

// Define essential tools
registerTool({
  name: 'testConnection',
  description: 'Test if AgentRPC is working correctly',
  schema: z.object({
    message: z.string().optional().default('Hello AgentRPC!').describe('Test message')
  }),
  handler: async ({ message = 'Hello AgentRPC!' }) => {
    return {
      success: true,
      message,
      timestamp: new Date().toISOString(),
      server: 'AgentRPC MCP Bridge',
      testPassed: true
    };
  }
});

registerTool({
  name: 'getSystemInfo',
  description: 'Get system information about the computer',
  schema: z.object({}),
  handler: async () => {
    const os = require('os');
    return {
      platform: os.platform(),
      architecture: os.arch(),
      nodeVersion: process.version,
      totalMemory: `${Math.round(os.totalmem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      freeMemory: `${Math.round(os.freemem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      cpuCount: os.cpus().length,
      uptime: `${Math.round(os.uptime() / 3600 * 100) / 100} hours`,
      hostname: os.hostname(),
      user: os.userInfo().username
    };
  }
});

registerTool({
  name: 'calculate',
  description: 'Perform mathematical calculations',
  schema: z.object({
    expression: z.string().describe('Mathematical expression to evaluate (e.g., "2 + 3 * 4", "sqrt(16)")')
  }),
  handler: async ({ expression }) => {
    try {
      // Basic sanitization - only allow numbers, operators, and basic functions
      const sanitized = expression.replace(/[^0-9+\-*/.() ]/g, '');
      const result = eval(sanitized);
      
      return {
        expression,
        result,
        formatted: `${expression} = ${result}`
      };
    } catch (error) {
      throw new Error(`Invalid mathematical expression: ${error.message}`);
    }
  }
});

registerTool({
  name: 'listFiles',
  description: 'List files in a directory (read-only)',
  schema: z.object({
    directory: z.string().describe('Directory path to list')
  }),
  handler: async ({ directory }) => {
    try {
      const items = fs.readdirSync(directory, { withFileTypes: true });
      return {
        directory,
        items: items.map(item => ({
          name: item.name,
          type: item.isDirectory() ? 'directory' : 'file',
          path: path.join(directory, item.name)
        })),
        count: items.length,
        scannedAt: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  }
});

registerTool({
  name: 'readFile',
  description: 'Read contents of a local file (read-only)',
  schema: z.object({
    filePath: z.string().describe('Path to the file to read'),
    encoding: z.string().default('utf8').describe('File encoding (utf8, binary, etc.)')
  }),
  handler: async ({ filePath, encoding = 'utf8' }) => {
    try {
      const content = fs.readFileSync(filePath, encoding);
      const stats = fs.statSync(filePath);
      
      return {
        content,
        filePath,
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        encoding
      };
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }
});

registerTool({
  name: 'checkWebsite',
  description: 'Check if a website is responding',
  schema: z.object({
    url: z.string().describe('Website URL to check')
  }),
  handler: async ({ url }) => {
    try {
      const axios = require('axios');
      const startTime = Date.now();
      const response = await axios.get(url, { timeout: 10000 });
      const responseTime = Date.now() - startTime;
      
      return {
        url,
        status: 'UP',
        statusCode: response.status,
        responseTime: `${responseTime}ms`,
        checkedAt: new Date().toISOString()
      };
    } catch (error) {
      return {
        url,
        status: 'DOWN',
        error: error.message,
        checkedAt: new Date().toISOString()
      };
    }
  }
});

console.error(`[INFO] ✅ Registered ${tools.length} tools for MCP`);

// MCP Protocol Handlers
server.setRequestHandler('tools/list', async () => {
  console.error('[DEBUG] tools/list called');
  return { tools };
});

server.setRequestHandler('resources/list', async () => {
  console.error('[DEBUG] resources/list called');
  return { resources: [] };
});

server.setRequestHandler('prompts/list', async () => {
  console.error('[DEBUG] prompts/list called');
  return { prompts: [] };
});

server.setRequestHandler('tools/call', async (request) => {
  const { name, arguments: args = {} } = request.params;
  console.error(`[DEBUG] tools/call for: ${name}`);
  
  try {
    if (toolHandlers.has(name)) {
      const handler = toolHandlers.get(name);
      const result = await handler(args);
      
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } else {
      throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    console.error(`[ERROR] Tool execution failed for ${name}:`, error.message);
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          success: false,
          error: error.message,
          tool: name,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
});

console.error('[INFO] ✅ MCP handlers registered');

// Start AgentRPC connection (optional - bridge works without it)
rpc.listen().then(() => {
  console.error('[INFO] ✅ Connected to AgentRPC platform');
}).catch(error => {
  console.error(`[WARN] AgentRPC platform connection failed: ${error.message}`);
  console.error('[INFO] 💡 Bridge will continue without AgentRPC platform coordination');
});

// Start MCP server
const transport = new StdioServerTransport();
server.connect(transport).then(() => {
  console.error('[INFO] 🔗 MCP Bridge connected and ready for Claude Desktop!');
}).catch((error) => {
  console.error(`[ERROR] Failed to start MCP Bridge: ${error.message}`);
  process.exit(1);
});
