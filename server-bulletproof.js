require('dotenv').config();
const { AgentRPC } = require('agentrpc');
const { z, ZodError } = require('zod');

// Check if we're running in MCP mode
const isMcpMode = process.env.AGENTRPC_MODE === 'mcp';

// MCP-specific imports (with error handling)
let zodToJsonSchema;
if (isMcpMode) {
  try {
    zodToJsonSchema = require('zod-to-json-schema').zodToJsonSchema;
  } catch (error) {
    console.error('❌ MCP mode requires zod-to-json-schema package.');
    process.exit(1);
  }
}

// Initialize AgentRPC (with error handling)
let rpc;
try {
  rpc = new AgentRPC({
    apiSecret: process.env.AGENTRPC_API_SECRET,
  });
} catch (error) {
  console.error('❌ Failed to initialize AgentRPC:', error.message);
  process.exit(1);
}

// Tool collection for MCP mode
const mcpTools = [];
const mcpToolHandlers = new Map();

// Helper function for structured logging
const log = {
  info: (...args) => console.log(`[INFO] ${new Date().toISOString()}:`, ...args),
  warn: (...args) => console.warn(`[WARN] ${new Date().toISOString()}:`, ...args),
  error: (...args) => console.error(`[ERROR] ${new Date().toISOString()}:`, ...args),
  debug: (...args) => console.debug(`[DEBUG] ${new Date().toISOString()}:`, ...args),
};

log.info(`🚀 Starting AgentRPC Bulletproof Server in ${isMcpMode ? 'MCP' : 'AgentRPC'} mode...`);

// Safe tool registration function
function registerTool(name, description, schema, handler) {
  try {
    const toolDef = { name, description, schema, handler };
    
    if (isMcpMode) {
      try {
        const inputSchema = zodToJsonSchema(schema, {
          name: `${name}Schema`,
          $refStrategy: 'none'
        });
        
        mcpTools.push({
          name,
          description,
          inputSchema
        });
        
        mcpToolHandlers.set(name, handler);
        log.debug(`📝 Collected MCP tool: ${name}`);
      } catch (error) {
        log.error(`❌ Failed to convert tool ${name} for MCP:`, error.message);
        return false;
      }
    }
    
    // Register with AgentRPC (with error handling)
    try {
      rpc.register(toolDef);
      log.info(`✅ Registered tool: ${name}`);
      return true;
    } catch (error) {
      log.error(`❌ Failed to register tool ${name} with AgentRPC:`, error.message);
      return false;
    }
  } catch (error) {
    log.error(`❌ Failed to create tool ${name}:`, error.message);
    return false;
  }
}

let successfulTools = 0;

// Tool 1: Test Connection (Bulletproof)
try {
  if (registerTool(
    'testConnection',
    'Test if AgentRPC is working correctly', 
    z.object({
      message: z.string().optional().default('Hello!')
    }),
    async ({ message = 'Hello!' }) => {
      return {
        success: true,
        message,
        timestamp: new Date().toISOString(),
        server: 'Bulletproof AgentRPC',
        status: 'working'
      };
    }
  )) successfulTools++;
} catch (error) {
  log.error('❌ Failed to register testConnection:', error.message);
}

// Tool 2: System Info (Safe)
try {
  if (registerTool(
    'getSystemInfo',
    'Get basic system information',
    z.object({}),
    async () => {
      try {
        const os = require('os');
        return {
          platform: os.platform() || 'unknown',
          nodeVersion: process.version || 'unknown',
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          error: 'Failed to get system info',
          timestamp: new Date().toISOString()
        };
      }
    }
  )) successfulTools++;
} catch (error) {
  log.error('❌ Failed to register getSystemInfo:', error.message);
}

// Tool 3: Simple Echo (Always works)
try {
  if (registerTool(
    'echo',
    'Echo back any message',
    z.object({
      message: z.string().describe('Message to echo back')
    }),
    async ({ message }) => {
      return {
        echo: message,
        timestamp: new Date().toISOString(),
        length: message.length
      };
    }
  )) successfulTools++;
} catch (error) {
  log.error('❌ Failed to register echo:', error.message);
}

// Tool 4: Current Time (Always works)
try {
  if (registerTool(
    'getCurrentTime',
    'Get current date and time',
    z.object({}),
    async () => {
      const now = new Date();
      return {
        timestamp: now.toISOString(),
        local: now.toString(),
        unix: now.getTime(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown'
      };
    }
  )) successfulTools++;
} catch (error) {
  log.error('❌ Failed to register getCurrentTime:', error.message);
}

log.info(`🎉 Successfully registered ${successfulTools} bulletproof tools`);

// MCP Protocol Implementation (Bulletproof)
if (isMcpMode) {
  log.info('🔗 Starting MCP server...');
  log.info(`🛠️  ${mcpTools.length} tools available for MCP`);
  
  function sendResponse(id, result, error = null) {
    try {
      const response = {
        jsonrpc: '2.0',
        id: id,
        ...(error ? { error } : { result })
      };
      console.log(JSON.stringify(response));
    } catch (err) {
      log.error('❌ Failed to send response:', err.message);
    }
  }
  
  process.stdin.setEncoding('utf8');
  process.stdin.on('data', async (data) => {
    try {
      const lines = data.toString().trim().split('\n');
      
      for (const line of lines) {
        if (!line.trim()) continue;
        
        try {
          const request = JSON.parse(line);
          log.debug(`[MCP] Received: ${request.method}`);
          
          if (request.method === 'initialize') {
            sendResponse(request.id, {
              protocolVersion: '2024-11-05',
              capabilities: { tools: {} },
              serverInfo: {
                name: 'agentrpc-bulletproof',
                version: '1.0.0'
              }
            });
          }
          else if (request.method === 'tools/list') {
            sendResponse(request.id, { tools: mcpTools });
          }
          else if (request.method === 'tools/call') {
            const { name, arguments: args = {} } = request.params;
            
            if (mcpToolHandlers.has(name)) {
              try {
                const handler = mcpToolHandlers.get(name);
                const result = await handler(args);
                
                sendResponse(request.id, {
                  content: [{
                    type: 'text',
                    text: JSON.stringify(result, null, 2)
                  }]
                });
              } catch (error) {
                log.error(`[MCP] Tool error for ${name}:`, error.message);
                sendResponse(request.id, {
                  content: [{
                    type: 'text',
                    text: JSON.stringify({
                      success: false,
                      error: error.message,
                      tool: name
                    }, null, 2)
                  }]
                });
              }
            } else {
              sendResponse(request.id, null, { code: -1, message: `Unknown tool: ${name}` });
            }
          }
          else if (request.method === 'resources/list') {
            sendResponse(request.id, { resources: [] });
          }
          else if (request.method === 'prompts/list') {
            sendResponse(request.id, { prompts: [] });
          }
          else {
            sendResponse(request.id, null, { code: -1, message: `Unknown method: ${request.method}` });
          }
        } catch (parseError) {
          log.error(`[MCP] Parse error:`, parseError.message);
        }
      }
    } catch (error) {
      log.error(`[MCP] Data processing error:`, error.message);
    }
  });
  
  // AgentRPC connection (optional)
  try {
    log.info('🔗 Attempting AgentRPC platform connection...');
    rpc.listen();
    log.info('✅ Connected to AgentRPC platform');
  } catch (error) {
    log.warn(`⚠️  AgentRPC platform connection failed: ${error.message}`);
    log.info('💵 MCP server continuing without platform coordination');
  }
  
  log.info('✨ Bulletproof MCP Bridge is ready!');
  log.info('🎤 Waiting for Claude Desktop connection...');
  
} else {
  log.info('🔗 Starting AgentRPC server...');
  
  try {
    rpc.listen();
    log.info('✨ Bulletproof AgentRPC server is ready!');
  } catch (error) {
    log.error(`❌ Failed to start AgentRPC server: ${error.message}`);
    process.exit(1);
  }
}

log.info('🔧 Available bulletproof tools:');
log.info('   🧪 testConnection - Test connection');
log.info('   🔧 getSystemInfo - System info');
log.info('   📢 echo - Echo messages');
log.info('   ⏰ getCurrentTime - Current time');

// Bulletproof error handling
process.on('unhandledRejection', (reason) => {
  log.error('❌ Unhandled Promise Rejection:', reason);
  // DON'T exit - keep running
});

process.on('uncaughtException', (error) => {
  log.error('❌ Uncaught Exception:', error.message);
  // DON'T exit - keep running
});

process.on('SIGINT', () => {
  log.info('\n🛑 Shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log.info('\n🛑 Graceful shutdown...');
  process.exit(0);
});
