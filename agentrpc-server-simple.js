#!/usr/bin/env node

require('dotenv').config();
const { AgentRPC } = require('agentrpc');
const fs = require('fs');
const path = require('path');
const { z } = require('zod');

// Initialize AgentRPC
const rpc = new AgentRPC({
  apiSecret: process.env.AGENTRPC_API_SECRET,
});

console.log('🚀 Starting Simple AgentRPC Server...');
console.log('📋 This will register 8 tools with the AgentRPC platform');
console.log('🔗 Check your dashboard at: https://app.agentrpc.com/clusters/01JWJFP133VC25GQNQSV91ZX6E');
console.log('');

// Enhanced logging
const log = {
  info: (...args) => console.log(`[INFO] ${new Date().toISOString()}:`, ...args),
  warn: (...args) => console.warn(`[WARN] ${new Date().toISOString()}:`, ...args),
  error: (...args) => console.error(`[ERROR] ${new Date().toISOString()}:`, ...args),
  debug: (...args) => console.debug(`[DEBUG] ${new Date().toISOString()}:`, ...args),
};

let toolCount = 0;

// 1. Test Connection Tool
rpc.register({
  name: 'testConnection',
  description: 'Test if AgentRPC is working correctly',
  schema: z.object({
    message: z.string().optional().default('Hello AgentRPC!').describe('Test message')
  }),
  handler: async ({ message = 'Hello AgentRPC!' }) => {
    return {
      success: true,
      message,
      timestamp: new Date().toISOString(),
      server: 'Simple AgentRPC Server',
      testPassed: true,
      toolsRegistered: toolCount
    };
  }
});
toolCount++;
log.info('✅ Registered: testConnection');

// 2. System Info Tool
rpc.register({
  name: 'getSystemInfo',
  description: 'Get system information about the computer',
  schema: z.object({}),
  handler: async () => {
    const os = require('os');
    return {
      platform: os.platform(),
      architecture: os.arch(),
      nodeVersion: process.version,
      totalMemory: `${Math.round(os.totalmem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      freeMemory: `${Math.round(os.freemem() / 1024 / 1024 / 1024 * 100) / 100} GB`,
      cpuCount: os.cpus().length,
      uptime: `${Math.round(os.uptime() / 3600 * 100) / 100} hours`,
      hostname: os.hostname(),
      user: os.userInfo().username,
      workingDirectory: process.cwd()
    };
  }
});
toolCount++;
log.info('✅ Registered: getSystemInfo');

// 3. Calculator Tool
rpc.register({
  name: 'calculate',
  description: 'Perform mathematical calculations',
  schema: z.object({
    expression: z.string().describe('Mathematical expression to evaluate (e.g., "2 + 3 * 4", "sqrt(16)")')
  }),
  handler: async ({ expression }) => {
    try {
      // Basic sanitization for safety
      const sanitized = expression.replace(/[^0-9+\-*/.() ]/g, '');
      const result = eval(sanitized);
      
      return {
        expression,
        result,
        formatted: `${expression} = ${result}`,
        calculatedAt: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Invalid mathematical expression: ${error.message}`);
    }
  }
});
toolCount++;
log.info('✅ Registered: calculate');

// 4. File List Tool
rpc.register({
  name: 'listFiles',
  description: 'List files in a directory (read-only)',
  schema: z.object({
    directory: z.string().describe('Directory path to list')
  }),
  handler: async ({ directory }) => {
    try {
      const items = fs.readdirSync(directory, { withFileTypes: true });
      return {
        directory,
        items: items.map(item => ({
          name: item.name,
          type: item.isDirectory() ? 'directory' : 'file',
          path: path.join(directory, item.name)
        })),
        count: items.length,
        scannedAt: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  }
});
toolCount++;
log.info('✅ Registered: listFiles');

// 5. Read File Tool
rpc.register({
  name: 'readFile',
  description: 'Read contents of a local file (read-only)',
  schema: z.object({
    filePath: z.string().describe('Path to the file to read'),
    encoding: z.string().default('utf8').describe('File encoding (utf8, binary, etc.)')
  }),
  handler: async ({ filePath, encoding = 'utf8' }) => {
    try {
      const content = fs.readFileSync(filePath, encoding);
      const stats = fs.statSync(filePath);
      
      return {
        content,
        filePath,
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        encoding,
        readAt: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }
});
toolCount++;
log.info('✅ Registered: readFile');

// 6. Website Checker Tool
rpc.register({
  name: 'checkWebsite',
  description: 'Check if a website is responding',
  schema: z.object({
    url: z.string().describe('Website URL to check')
  }),
  handler: async ({ url }) => {
    try {
      const axios = require('axios');
      const startTime = Date.now();
      const response = await axios.get(url, { timeout: 10000 });
      const responseTime = Date.now() - startTime;
      
      return {
        url,
        status: 'UP',
        statusCode: response.status,
        responseTime: `${responseTime}ms`,
        checkedAt: new Date().toISOString()
      };
    } catch (error) {
      return {
        url,
        status: 'DOWN',
        error: error.message,
        checkedAt: new Date().toISOString()
      };
    }
  }
});
toolCount++;
log.info('✅ Registered: checkWebsite');

// 7. Get Weather Tool (mock for testing)
rpc.register({
  name: 'getWeather',
  description: 'Get weather information for any location',
  schema: z.object({
    location: z.string().describe('Location to get weather for')
  }),
  handler: async ({ location }) => {
    // Mock weather data for testing
    return {
      location,
      temperature: '75°F',
      condition: 'sunny',
      humidity: '45%',
      windSpeed: '5 mph',
      timestamp: new Date().toISOString(),
      note: 'Mock weather data for testing purposes'
    };
  }
});
toolCount++;
log.info('✅ Registered: getWeather');

// 8. Current Time Tool
rpc.register({
  name: 'getCurrentTime',
  description: 'Get current date and time information',
  schema: z.object({
    timezone: z.string().optional().describe('Timezone (optional)')
  }),
  handler: async ({ timezone }) => {
    const now = new Date();
    return {
      timestamp: now.toISOString(),
      localTime: now.toLocaleString(),
      timezone: timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      unixTimestamp: Math.floor(now.getTime() / 1000),
      dayOfWeek: now.toLocaleDateString('en-US', { weekday: 'long' }),
      formattedDate: now.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    };
  }
});
toolCount++;
log.info('✅ Registered: getCurrentTime');

log.info(`🎉 Successfully registered ${toolCount} tools with AgentRPC platform`);
log.info('');

// Start the server
log.info('🔗 Connecting to AgentRPC platform...');
rpc.listen()
  .then(() => {
    log.info('✅ Connected to AgentRPC platform successfully!');
    log.info('🌐 Your tools should now appear in the dashboard');
    log.info('📊 Dashboard: https://app.agentrpc.com/clusters/01JWJFP133VC25GQNQSV91ZX6E');
    log.info('');
    log.info('🛠️  Available tools:');
    log.info('   1. testConnection - Test the connection');
    log.info('   2. getSystemInfo - System information');
    log.info('   3. calculate - Math calculator');
    log.info('   4. listFiles - List directory contents');
    log.info('   5. readFile - Read file contents');
    log.info('   6. checkWebsite - Website uptime check');
    log.info('   7. getWeather - Weather information (mock)');
    log.info('   8. getCurrentTime - Current date and time');
    log.info('');
    log.info('✨ Server is running and ready for requests!');
    log.info('💡 Press Ctrl+C to stop the server');
  })
  .catch(error => {
    log.error(`❌ Failed to connect to AgentRPC platform: ${error.message}`);
    log.error('🔍 Check your API secret and network connection');
    process.exit(1);
  });

// Enhanced error handling
process.on('unhandledRejection', (reason, promise) => {
  log.error('❌ Unhandled Promise Rejection:', reason);
});

process.on('uncaughtException', (error) => {
  log.error('❌ Uncaught Exception:', error.message);
  log.error('❌ Stack:', error.stack);
});

process.on('SIGINT', () => {
  log.info('');
  log.info('🛑 Shutting down AgentRPC server...');
  log.info('👋 Goodbye!');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log.info('');
  log.info('🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
