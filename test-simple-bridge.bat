@echo off
echo 🧪 Testing Simple AgentRPC MCP Bridge
echo 📊 8 Essential Tools Only
echo.

REM Set environment variables
set AGENTRPC_API_SECRET=sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799

echo 📦 Test Environment:
echo   AGENTRPC_API_SECRET: %AGENTRPC_API_SECRET%
echo   Working Directory: %CD%
echo   Bridge File: mcp-bridge-simple.js
echo.

echo 🔍 Pre-Test Checks:
if exist "mcp-bridge-simple.js" (
    echo   ✅ Simple bridge file exists
) else (
    echo   ❌ Bridge file missing
    goto :error
)

if exist "node_modules\@modelcontextprotocol" (
    echo   ✅ MCP SDK installed
) else (
    echo   ❌ MCP SDK missing
    goto :error
)

if exist "node_modules\agentrpc" (
    echo   ✅ AgentRPC package installed
) else (
    echo   ❌ AgentRPC missing
    goto :error
)

echo.
echo 🚀 Starting simple bridge test...
echo   💡 Press Ctrl+C to stop the test
echo   📊 Should show 8 tools registered
echo   🔍 Watch for "Simple MCP Bridge connected and ready!"
echo.

node mcp-bridge-simple.js
goto :end

:error
echo.
echo ❌ Test failed - missing dependencies or files
echo 💡 Run the following to fix:
echo   npm install @modelcontextprotocol/sdk
echo   npm install agentrpc
echo   npm install zod-to-json-schema
echo.

:end
pause
