@echo off
echo 🧪 Testing Comprehensive AgentRPC MCP Bridge
echo 📋 Verifying Master Analysis Report Solution
echo.

REM Set environment variables
set AGENTRPC_API_SECRET=sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799

echo 📦 Test Environment:
echo   AGENTRPC_API_SECRET: %AGENTRPC_API_SECRET%
echo   Working Directory: %CD%
echo   Bridge File: mcp-bridge-comprehensive.js
echo.

echo 🔍 Pre-Test Checks:
if exist "mcp-bridge-comprehensive.js" (
    echo   ✅ Bridge file exists
) else (
    echo   ❌ Bridge file missing
    goto :error
)

if exist "node_modules\@modelcontextprotocol" (
    echo   ✅ MCP SDK installed
) else (
    echo   ❌ MCP SDK missing - run: npm install @modelcontextprotocol/sdk
    goto :error
)

if exist "node_modules\agentrpc" (
    echo   ✅ AgentRPC package installed
) else (
    echo   ❌ AgentRPC missing - run: npm install agentrpc
    goto :error
)

echo.
echo 🚀 Starting bridge test...
echo   💡 Press Ctrl+C to stop the test
echo   📊 Watch for successful initialization messages
echo.

node mcp-bridge-comprehensive.js
goto :end

:error
echo.
echo ❌ Test failed - missing dependencies
echo 💡 Run the following commands to fix:
echo   npm install @modelcontextprotocol/sdk
echo   npm install agentrpc
echo   npm install zod-to-json-schema
echo.

:end
pause
