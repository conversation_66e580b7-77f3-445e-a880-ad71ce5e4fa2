# Fix <PERSON> "AgentRPC Disconnected" Issue

## The Problem
Claude <PERSON> shows "agentrpc disconnected" because it can't find the MCP bridge configuration.

## The Solution

### Step 1: Copy Configuration File
1. **Copy** the file `claude_desktop_config_final.json` from this directory
2. **Navigate** to: `C:\Users\<USER>\AppData\Roaming\Claude\`
3. **Create the directory** if it doesn't exist
4. **Paste and rename** the file to: `claude_desktop_config.json`

### Step 2: Start the MCP Bridge
1. **Open a terminal** in this directory (`C:\Users\<USER>\Documents\agentrpc-toolkit`)
2. **Run**: `node mcp-bridge-simple.js`
3. **Keep the terminal open** (this is the bridge <PERSON> connects to)

### Step 3: Restart Claude Desktop
1. **Completely close** Claude Desktop
2. **Reopen** Claude Desktop
3. **Check** that "agentrpc-simple" appears in available tools

## What This Does

The configuration tells <PERSON> to:
- ✅ Look for an MCP server called "agentrpc-simple"
- ✅ Run `node mcp-bridge-simple.js` in your toolkit directory
- ✅ Use your AgentRPC API secret for authentication
- ✅ Connect to your 8 AgentRPC tools through the bridge

## Expected Results

After following these steps:
- ✅ No more "agentrpc disconnected" messages
- ✅ 8 AgentRPC tools available in Claude Desktop
- ✅ Tools work through both AgentRPC dashboard AND Claude Desktop

## Troubleshooting

### If still disconnected:
1. **Check** that `mcp-bridge-simple.js` is running
2. **Verify** the config file is in the correct location
3. **Restart** Claude Desktop completely
4. **Check** the terminal running the bridge for error messages

### If tools don't appear:
1. **Ensure** both servers are running:
   - AgentRPC server: `node agentrpc-server-simple.js` (for dashboard)
   - MCP bridge: `node mcp-bridge-simple.js` (for Claude Desktop)
2. **Check** your API secret is correct
3. **Verify** the working directory in the config

## Quick Test

Once everything is set up, test with Claude Desktop:
```
Use the testConnection tool to verify the AgentRPC bridge is working
```

You should get a response showing the bridge is connected and working!
