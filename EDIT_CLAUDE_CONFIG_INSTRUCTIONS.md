# How to Edit Your Existing Claude Desktop Config

## Step 1: Open Your Config File
1. Navigate to: `C:\Users\<USER>\AppData\Roaming\Claude\`
2. Open `claude_desktop_config.json` (or `claude_desktop_config`) in a text editor

## Step 2: Add AgentRPC Configuration

Your config file probably looks something like this:
```json
{
  "mcpServers": {
    "existing-server-1": {
      // ... existing configuration
    },
    "existing-server-2": {
      // ... existing configuration  
    }
  }
}
```

**Add this section** inside the `mcpServers` object (add a comma after the last existing server):

```json
"agentrpc-simple": {
  "command": "node",
  "args": ["mcp-bridge-simple.js"],
  "cwd": "C:\\Users\\<USER>\\Documents\\agentrpc-toolkit",
  "env": {
    "AGENTRPC_API_SECRET": "sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799"
  }
}
```

## Step 3: Final Result Should Look Like This

```json
{
  "mcpServers": {
    "existing-server-1": {
      // ... your existing configuration
    },
    "existing-server-2": {
      // ... your existing configuration  
    },
    "agentrpc-simple": {
      "command": "node",
      "args": ["mcp-bridge-simple.js"],
      "cwd": "C:\\Users\\<USER>\\Documents\\agentrpc-toolkit",
      "env": {
        "AGENTRPC_API_SECRET": "sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799"
      }
    }
  }
}
```

## Step 4: Save and Test

1. **Save** the file
2. **Start the MCP bridge**: `node mcp-bridge-simple.js`
3. **Restart Claude Desktop** completely
4. **Check** for "agentrpc-simple" in available tools

## Important Notes

- ✅ **Don't remove** your existing server configurations
- ✅ **Add commas** between server configurations
- ✅ **Keep the JSON format** valid (proper brackets, quotes, commas)
- ✅ **Double-check** the file path and API secret are correct

## If You Get JSON Errors

If Claude Desktop shows JSON parsing errors:
1. Use a JSON validator online to check your syntax
2. Make sure all brackets `{}` and quotes `""` are properly matched
3. Ensure commas are placed correctly between sections

## Quick Test

After editing, test with Claude Desktop:
```
Use the testConnection tool
```

You should get a response showing the AgentRPC bridge is working!
