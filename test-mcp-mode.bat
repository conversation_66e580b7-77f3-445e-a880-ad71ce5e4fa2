@echo off
echo =========================================
echo     Testing AgentRPC MCP Mode
echo =========================================
echo.

cd /d C:\Users\<USER>\Documents\agentrpc-toolkit

echo Setting environment variables...
set AGENTRPC_API_SECRET=sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799
set AGENTRPC_MODE=mcp

echo.
echo Starting server in MCP mode...
echo This should now work with Claude Desktop!
echo.

node server.js

echo.
echo ===========================================
echo Server stopped. Press any key to exit.
pause
