require('dotenv').config();\nconst { AgentRPC } = require('agentrpc');\nconst fs = require('fs');\nconst path = require('path');\nconst { z, ZodError } = require('zod');\n\n// Check if we're running in MCP mode\nconst isMcpMode = process.env.AGENTRPC_MODE === 'mcp';\n\n// MCP-specific imports\nlet zodToJsonSchema;\nif (isMcpMode) {\n  try {\n    zodToJsonSchema = require('zod-to-json-schema').zodToJsonSchema;\n  } catch (error) {\n    console.error('❌ MCP mode requires zod-to-json-schema package. Install with: npm install zod-to-json-schema');\n    process.exit(1);\n  }\n}\n\n// Initialize AgentRPC\nconst rpc = new AgentRPC({\n  apiSecret: process.env.AGENTRPC_API_SECRET,\n});\n\n// Tool collection for MCP mode\nconst mcpTools = [];\nconst mcpToolHandlers = new Map();\n\n// Create necessary directories\nconst dirs = ['temp', 'reports', 'backups'];\ndirs.forEach(dir => {\n  if (!fs.existsSync(dir)) {\n    fs.mkdirSync(dir, { recursive: true });\n  }\n});\n\n// Helper function for structured logging\nconst log = {\n  info: (...args) => console.log(`[INFO] ${new Date().toISOString()}:`, ...args),\n  warn: (...args) => console.warn(`[WARN] ${new Date().toISOString()}:`, ...args),\n  error: (...args) => console.error(`[ERROR] ${new Date().toISOString()}:`, ...args),\n  debug: (...args) => console.debug(`[DEBUG] ${new Date().toISOString()}:`, ...args),\n};\n\nlog.info(`🚀 Starting AgentRPC Essential Toolkit in ${isMcpMode ? 'MCP' : 'AgentRPC'} mode...`);\nlog.info('📦 Loading essential tools only...');\n\n// Create a proxy RPC object for tool collection in MCP mode\nconst toolCollectorRpc = {\n  register: (toolDef) => {\n    if (isMcpMode) {\n      try {\n        const inputSchema = zodToJsonSchema(toolDef.schema, {\n          name: `${toolDef.name}Schema`,\n          $refStrategy: 'none'\n        });\n        \n        mcpTools.push({\n          name: toolDef.name,\n          description: toolDef.description,\n          inputSchema\n        });\n        \n        mcpToolHandlers.set(toolDef.name, toolDef.handler);\n        log.debug(`📝 Collected MCP tool: ${toolDef.name}`);\n      } catch (error) {\n        log.error(`❌ Failed to convert tool ${toolDef.name} for MCP:`, error.message);\n      }\n    }\n    \n    try {\n      return rpc.register(toolDef);\n    } catch (error) {\n      log.error(`❌ Failed to register tool ${toolDef.name} with AgentRPC:`, error.message);\n      return Promise.resolve({ success: false, error: error.message });\n    }\n  }\n};\n\n// Define essential tools inline (no external dependencies)\nlet totalTools = 0;\n\n// Test Connection Tool\ntoolCollectorRpc.register({\n  name: 'testConnection',\n  description: 'Test if AgentRPC is working correctly',\n  schema: z.object({\n    message: z.string().optional().default('Hello AgentRPC!').describe('Test message')\n  }),\n  handler: async ({ message = 'Hello AgentRPC!' }) => {\n    return {\n      success: true,\n      message,\n      timestamp: new Date().toISOString(),\n      server: 'AgentRPC Essential Tools',\n      testPassed: true\n    };\n  }\n});\ntotalTools++;\n\n// System Info Tool\ntoolCollectorRpc.register({\n  name: 'getSystemInfo',\n  description: 'Get system information about the computer',\n  schema: z.object({}),\n  handler: async () => {\n    const os = require('os');\n    return {\n      platform: os.platform(),\n      architecture: os.arch(),\n      nodeVersion: process.version,\n      totalMemory: `${Math.round(os.totalmem() / 1024 / 1024 / 1024 * 100) / 100} GB`,\n      freeMemory: `${Math.round(os.freemem() / 1024 / 1024 / 1024 * 100) / 100} GB`,\n      cpuCount: os.cpus().length,\n      uptime: `${Math.round(os.uptime() / 3600 * 100) / 100} hours`,\n      hostname: os.hostname(),\n      user: os.userInfo().username\n    };\n  },\n});\ntotalTools++;\n\n// Calculator Tool\ntoolCollectorRpc.register({\n  name: 'calculate',\n  description: 'Perform mathematical calculations',\n  schema: z.object({\n    expression: z.string().describe('Mathematical expression to evaluate (e.g., \"2 + 3 * 4\", \"sqrt(16)\")')\n  }),\n  handler: async ({ expression }) => {\n    try {\n      const sanitized = expression.replace(/[^0-9+\\-*/.() ]/g, '');\n      const result = eval(sanitized);\n      \n      return {\n        expression,\n        result,\n        formatted: `${expression} = ${result}`\n      };\n    } catch (error) {\n      throw new Error(`Invalid mathematical expression: ${error.message}`);\n    }\n  }\n});\ntotalTools++;\n\n// File List Tool (safe, read-only)\ntoolCollectorRpc.register({\n  name: 'listFiles',\n  description: 'List files in a directory (read-only)',\n  schema: z.object({\n    directory: z.string().describe('Directory path to list')\n  }),\n  handler: async ({ directory }) => {\n    try {\n      const items = fs.readdirSync(directory, { withFileTypes: true });\n      return {\n        directory,\n        items: items.map(item => ({\n          name: item.name,\n          type: item.isDirectory() ? 'directory' : 'file',\n          path: path.join(directory, item.name)\n        })),\n        count: items.length,\n        scannedAt: new Date().toISOString()\n      };\n    } catch (error) {\n      throw new Error(`Failed to list directory: ${error.message}`);\n    }\n  },\n});\ntotalTools++;\n\n// Read File Tool (safe, read-only)\ntoolCollectorRpc.register({\n  name: 'readFile',\n  description: 'Read contents of a local file (read-only)',\n  schema: z.object({\n    filePath: z.string().describe('Path to the file to read'),\n    encoding: z.string().default('utf8').describe('File encoding (utf8, binary, etc.)')\n  }),\n  handler: async ({ filePath, encoding = 'utf8' }) => {\n    try {\n      const content = fs.readFileSync(filePath, encoding);\n      const stats = fs.statSync(filePath);\n      \n      return {\n        content,\n        filePath,\n        size: stats.size,\n        lastModified: stats.mtime.toISOString(),\n        encoding\n      };\n    } catch (error) {\n      throw new Error(`Failed to read file: ${error.message}`);\n    }\n  }\n});\ntotalTools++;\n\n// Website Checker Tool (no external API needed)\ntoolCollectorRpc.register({\n  name: 'checkWebsite',\n  description: 'Check if a website is responding',\n  schema: z.object({\n    url: z.string().describe('Website URL to check')\n  }),\n  handler: async ({ url }) => {\n    try {\n      const axios = require('axios');\n      const startTime = Date.now();\n      const response = await axios.get(url, { timeout: 10000 });\n      const responseTime = Date.now() - startTime;\n      \n      return {\n        url,\n        status: 'UP',\n        statusCode: response.status,\n        responseTime: `${responseTime}ms`,\n        checkedAt: new Date().toISOString()\n      };\n    } catch (error) {\n      return {\n        url,\n        status: 'DOWN',\n        error: error.message,\n        checkedAt: new Date().toISOString()\n      };\n    }\n  }\n});\ntotalTools++;\n\nlog.info(`🎉 Successfully loaded ${totalTools} essential tools`);\n\n// MCP Protocol Implementation\nif (isMcpMode) {\n  log.info('🔗 Starting MCP server for Claude Desktop...');\n  log.info(`🛠️  ${mcpTools.length} tools available for MCP`);\n  \n  function sendResponse(id, result, error = null) {\n    const response = {\n      jsonrpc: '2.0',\n      id: id,\n      ...(error ? { error } : { result })\n    };\n    console.log(JSON.stringify(response));\n  }\n  \n  process.stdin.setEncoding('utf8');\n  process.stdin.on('data', async (data) => {\n    try {\n      const lines = data.toString().trim().split('\\n');\n      \n      for (const line of lines) {\n        if (!line.trim()) continue;\n        \n        const request = JSON.parse(line);\n        log.debug(`[MCP] Received: ${request.method}`);\n        \n        try {\n          if (request.method === 'initialize') {\n            sendResponse(request.id, {\n              protocolVersion: '2024-11-05',\n              capabilities: { tools: {} },\n              serverInfo: {\n                name: 'agentrpc-essential',\n                version: '1.0.0'\n              }\n            });\n          }\n          else if (request.method === 'tools/list') {\n            sendResponse(request.id, { tools: mcpTools });\n          }\n          else if (request.method === 'tools/call') {\n            const { name, arguments: args = {} } = request.params;\n            \n            if (mcpToolHandlers.has(name)) {\n              try {\n                const handler = mcpToolHandlers.get(name);\n                const result = await handler(args);\n                \n                sendResponse(request.id, {\n                  content: [{\n                    type: 'text',\n                    text: JSON.stringify(result, null, 2)\n                  }]\n                });\n              } catch (error) {\n                log.error(`[MCP] Tool execution error for ${name}:`, error.message);\n                sendResponse(request.id, {\n                  content: [{\n                    type: 'text',\n                    text: JSON.stringify({\n                      success: false,\n                      error: error.message,\n                      tool: name,\n                      timestamp: new Date().toISOString()\n                    }, null, 2)\n                  }]\n                });\n              }\n            } else {\n              sendResponse(request.id, null, { code: -1, message: `Unknown tool: ${name}` });\n            }\n          }\n          else if (request.method === 'resources/list') {\n            sendResponse(request.id, { resources: [] });\n          }\n          else if (request.method === 'prompts/list') {\n            sendResponse(request.id, { prompts: [] });\n          }\n          else {\n            log.debug(`[MCP] Unknown method: ${request.method}`);\n            sendResponse(request.id, null, { code: -1, message: `Unknown method: ${request.method}` });\n          }\n        } catch (methodError) {\n          log.error(`[MCP] Method execution error:`, methodError.message);\n          sendResponse(request.id, null, { code: -1, message: `Method execution failed: ${methodError.message}` });\n        }\n      }\n    } catch (error) {\n      log.error(`[MCP] Failed to process message:`, error.message);\n    }\n  });\n  \n  try {\n    log.info('🔗 Attempting AgentRPC platform connection...');\n    rpc.listen();\n    log.info('✅ Connected to AgentRPC platform for coordination');\n  } catch (error) {\n    log.error(`⚠️  AgentRPC platform connection failed: ${error.message}`);\n    log.info('💵 MCP server will continue without AgentRPC platform coordination');\n  }\n  \n  log.info('✨ AgentRPC Essential MCP Bridge is ready!');\n  log.info('🎤 Listening for MCP calls from Claude Desktop');\n  \n} else {\n  log.info('🔗 Starting AgentRPC server...');\n  \n  try {\n    rpc.listen();\n    log.info('✨ AgentRPC Essential Toolkit is ready!');\n  } catch (error) {\n    log.error(`❌ Failed to start AgentRPC server: ${error.message}`);\n    process.exit(1);\n  }\n}\n\nlog.info('🔧 Available essential tools:');\nlog.info('   🧪 testConnection - Test the connection');\nlog.info('   🔧 getSystemInfo - System information');\nlog.info('   🧮 calculate - Math calculator');\nlog.info('   📁 listFiles - List directory contents');\nlog.info('   📄 readFile - Read file contents');\nlog.info('   🌐 checkWebsite - Website uptime check');\n\n// Enhanced error handling\nprocess.on('unhandledRejection', (reason, promise) => {\n  log.error('❌ Unhandled Promise Rejection at:', promise);\n  log.error('❌ Reason:', reason);\n});\n\nprocess.on('uncaughtException', (error) => {\n  log.error('❌ Uncaught Exception:', error.message);\n  log.error('❌ Stack:', error.stack);\n  setTimeout(() => {\n    log.error('📴 Server may be unstable after uncaught exception.');\n  }, 1000);\n});\n\nprocess.on('SIGINT', () => {\n  log.info('\\n🛑 Shutting down AgentRPC Essential Toolkit...');\n  process.exit(0);\n});\n\nprocess.on('SIGTERM', () => {\n  log.info('\\n🛑 Received SIGTERM, shutting down gracefully...');\n  process.exit(0);\n});\n