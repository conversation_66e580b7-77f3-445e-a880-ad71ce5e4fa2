@echo off\necho =========================================\necho     AgentRPC ESSENTIAL Tools Only\necho       (No External APIs Required)\necho =========================================\necho.\n\ncd /d C:\\Users\\<USER>\\Documents\\agentrpc-toolkit\n\necho Setting environment variables...\nset AGENTRPC_API_SECRET=sk_01JWJFP133VC25GQNQSV91ZX6E_bf7e7f9408412bf4b8b25e748f4ba799\nset AGENTRPC_MODE=mcp\n\necho.\necho Starting ESSENTIAL server with only basic tools...\necho These tools require NO external APIs or database connections!\necho.\necho Available tools:\necho   🧪 testConnection - Test the connection\necho   🔧 getSystemInfo - System information\necho   🧮 calculate - Math calculator\necho   📁 listFiles - List directory contents\necho   📄 readFile - Read file contents\necho   🌐 checkWebsite - Website uptime check\necho.\n\nnode server-essential.js\n\necho.\necho ===========================================\necho Server stopped. Press any key to exit.\npause\n